mod settings;
mod region_selector;
mod recording;
mod gui;

use eframe::egui;
use gui::{AppState, main_window, recording_overlay};

fn main() -> Result<(), eframe::Error> {
    env_logger::init(); // Log to stderr (if you run with `RUST_LOG=debug`).

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([800.0, 600.0])
            .with_title("Screen Region Recorder")
            .with_icon(
                // Add an icon if you have one
                eframe::icon_data::from_png_bytes(&[]).unwrap_or_default(),
            ),
        ..Default::default()
    };

    eframe::run_native(
        "Screen Region Recorder",
        options,
        Box::new(|_cc| {
            // This gives us image support:
            egui_extras::install_image_loaders(&_cc.egui_ctx);

            Ok(Box::<ScreenRecorderApp>::default())
        }),
    )
}

struct ScreenRecorderApp {
    app_state: AppState,
}

impl Default for ScreenRecorderApp {
    fn default() -> Self {
        Self {
            app_state: AppState::new(),
        }
    }
}

impl eframe::App for ScreenRecorderApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Show main window
        main_window::show_main_window(ctx, &mut self.app_state);

        // Show recording overlay if active
        recording_overlay::show_recording_overlay(ctx, &mut self.app_state);

        // Request repaint for smooth animations
        ctx.request_repaint();
    }

    fn on_exit(&mut self, _gl: Option<&eframe::glow::Context>) {
        // Save settings on exit
        self.app_state.save_settings();
    }
}
