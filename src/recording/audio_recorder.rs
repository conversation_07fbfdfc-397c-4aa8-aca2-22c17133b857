use cpal::traits::{<PERSON><PERSON><PERSON><PERSON><PERSON>, HostTrait, StreamTrait};
use cpal::{<PERSON><PERSON>, Host, Stream, StreamConfig, SampleFormat, SampleRate};
use std::sync::{Arc, Mutex, mpsc};
use std::thread;
use std::collections::VecDeque;
use std::error::Error;

pub struct AudioRecorder {
    stream: Option<Stream>,
    audio_data: Arc<Mutex<VecDeque<f32>>>,
    is_recording: bool,
    sample_rate: u32,
    channels: u16,
}

impl AudioRecorder {
    pub fn new() -> Result<Self, Box<dyn Error>> {
        Ok(Self {
            stream: None,
            audio_data: Arc::new(Mutex::new(VecDeque::new())),
            is_recording: false,
            sample_rate: 44100,
            channels: 2,
        })
    }
    
    pub fn start_recording(&mut self) -> Result<(), Box<dyn Error>> {
        let host = cpal::default_host();
        let device = host.default_input_device()
            .ok_or("No input device available")?;
        
        let config = self.get_supported_config(&device)?;
        let sample_format = config.sample_format();
        let config: StreamConfig = config.into();
        
        self.sample_rate = config.sample_rate.0;
        self.channels = config.channels;
        
        let audio_data = Arc::clone(&self.audio_data);
        
        let stream = match sample_format {
            SampleFormat::F32 => {
                device.build_input_stream(
                    &config,
                    move |data: &[f32], _: &cpal::InputCallbackInfo| {
                        let mut buffer = audio_data.lock().unwrap();
                        for &sample in data {
                            buffer.push_back(sample);
                        }
                    },
                    |err| eprintln!("Audio recording error: {}", err),
                    None,
                )?
            }
            SampleFormat::I16 => {
                device.build_input_stream(
                    &config,
                    move |data: &[i16], _: &cpal::InputCallbackInfo| {
                        let mut buffer = audio_data.lock().unwrap();
                        for &sample in data {
                            // Convert i16 to f32
                            let normalized = sample as f32 / i16::MAX as f32;
                            buffer.push_back(normalized);
                        }
                    },
                    |err| eprintln!("Audio recording error: {}", err),
                    None,
                )?
            }
            SampleFormat::U16 => {
                device.build_input_stream(
                    &config,
                    move |data: &[u16], _: &cpal::InputCallbackInfo| {
                        let mut buffer = audio_data.lock().unwrap();
                        for &sample in data {
                            // Convert u16 to f32
                            let normalized = (sample as f32 - u16::MAX as f32 / 2.0) / (u16::MAX as f32 / 2.0);
                            buffer.push_back(normalized);
                        }
                    },
                    |err| eprintln!("Audio recording error: {}", err),
                    None,
                )?
            }
            _ => return Err("Unsupported sample format".into()),
        };
        
        stream.play()?;
        self.stream = Some(stream);
        self.is_recording = true;
        
        println!("Audio recording started: {} Hz, {} channels", self.sample_rate, self.channels);
        Ok(())
    }
    
    pub fn stop_recording(&mut self) -> Vec<f32> {
        self.is_recording = false;
        
        if let Some(stream) = self.stream.take() {
            drop(stream);
        }
        
        let mut buffer = self.audio_data.lock().unwrap();
        let audio_data: Vec<f32> = buffer.drain(..).collect();
        
        println!("Audio recording stopped. Captured {} samples", audio_data.len());
        audio_data
    }
    
    pub fn is_recording(&self) -> bool {
        self.is_recording
    }
    
    pub fn get_sample_rate(&self) -> u32 {
        self.sample_rate
    }
    
    pub fn get_channels(&self) -> u16 {
        self.channels
    }
    
    fn get_supported_config(&self, device: &Device) -> Result<cpal::SupportedStreamConfig, Box<dyn Error>> {
        let mut supported_configs = device.supported_input_configs()?;
        
        // Try to find a config with our preferred sample rate
        if let Some(config) = supported_configs.find(|c| {
            c.min_sample_rate() <= SampleRate(44100) && c.max_sample_rate() >= SampleRate(44100)
        }) {
            Ok(config.with_sample_rate(SampleRate(44100)))
        } else {
            // Fall back to the first available config
            device.supported_input_configs()?
                .next()
                .ok_or("No supported input config found")?
                .with_max_sample_rate()
                .into()
        }
    }
    
    pub fn get_available_devices() -> Result<Vec<String>, Box<dyn Error>> {
        let host = cpal::default_host();
        let mut devices = Vec::new();
        
        for device in host.input_devices()? {
            if let Ok(name) = device.name() {
                devices.push(name);
            }
        }
        
        Ok(devices)
    }
    
    pub fn save_audio_to_wav(
        audio_data: &[f32],
        sample_rate: u32,
        channels: u16,
        output_path: &std::path::Path,
    ) -> Result<(), Box<dyn Error>> {
        use std::fs::File;
        use std::io::{BufWriter, Write};
        
        let file = File::create(output_path)?;
        let mut writer = BufWriter::new(file);
        
        // WAV header
        let data_size = (audio_data.len() * 2) as u32; // 16-bit samples
        let file_size = 36 + data_size;
        
        // RIFF header
        writer.write_all(b"RIFF")?;
        writer.write_all(&file_size.to_le_bytes())?;
        writer.write_all(b"WAVE")?;
        
        // fmt chunk
        writer.write_all(b"fmt ")?;
        writer.write_all(&16u32.to_le_bytes())?; // chunk size
        writer.write_all(&1u16.to_le_bytes())?; // audio format (PCM)
        writer.write_all(&channels.to_le_bytes())?;
        writer.write_all(&sample_rate.to_le_bytes())?;
        writer.write_all(&(sample_rate * channels as u32 * 2).to_le_bytes())?; // byte rate
        writer.write_all(&(channels * 2).to_le_bytes())?; // block align
        writer.write_all(&16u16.to_le_bytes())?; // bits per sample
        
        // data chunk
        writer.write_all(b"data")?;
        writer.write_all(&data_size.to_le_bytes())?;
        
        // audio data (convert f32 to i16)
        for &sample in audio_data {
            let sample_i16 = (sample.clamp(-1.0, 1.0) * i16::MAX as f32) as i16;
            writer.write_all(&sample_i16.to_le_bytes())?;
        }
        
        writer.flush()?;
        println!("Audio saved to: {:?}", output_path);
        Ok(())
    }
}

impl Drop for AudioRecorder {
    fn drop(&mut self) {
        if self.is_recording {
            self.stop_recording();
        }
    }
}
