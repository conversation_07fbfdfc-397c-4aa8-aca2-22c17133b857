use global_hotkey::{GlobalHotKeyManager, HotKeyState, GlobalHotKeyEvent};
use global_hotkey::hotkey::{HotKey, Modifiers, Code};
use crate::gui::AppState;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

pub struct HotkeyManager {
    _manager: GlobalHotKeyManager,
    hotkeys: Vec<HotKey>,
}

impl HotkeyManager {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let manager = GlobalHotKeyManager::new()?;
        
        Ok(Self {
            _manager: manager,
            hotkeys: Vec::new(),
        })
    }
    
    pub fn register_hotkeys(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Ctrl+Shift+R - Start/Stop recording
        let record_hotkey = HotKey::new(
            Some(Modifiers::CONTROL | Modifiers::SHIFT),
            Code::KeyR,
        );
        
        // Ctrl+Shift+S - Select region
        let select_hotkey = HotKey::new(
            Some(Modifiers::CONTROL | Modifiers::SHIFT),
            Code::KeyS,
        );
        
        // Ctrl+Shift+P - Pause/Resume recording
        let pause_hotkey = HotKey::new(
            Some(Modifiers::CONTROL | Modifiers::SHIFT),
            Code::KeyP,
        );
        
        // Ctrl+Shift+Q - Quick screenshot
        let screenshot_hotkey = HotKey::new(
            Some(Modifiers::CONTROL | Modifiers::SHIFT),
            Code::KeyQ,
        );
        
        self._manager.register(record_hotkey)?;
        self._manager.register(select_hotkey)?;
        self._manager.register(pause_hotkey)?;
        self._manager.register(screenshot_hotkey)?;
        
        self.hotkeys.push(record_hotkey);
        self.hotkeys.push(select_hotkey);
        self.hotkeys.push(pause_hotkey);
        self.hotkeys.push(screenshot_hotkey);
        
        Ok(())
    }
}

pub fn init_hotkeys(app_state: Arc<Mutex<AppState>>) -> Result<(), Box<dyn std::error::Error>> {
    let mut hotkey_manager = HotkeyManager::new()?;
    hotkey_manager.register_hotkeys()?;
    
    // Start hotkey event listener in a separate thread
    thread::spawn(move || {
        let receiver = GlobalHotKeyEvent::receiver();
        
        loop {
            if let Ok(event) = receiver.try_recv() {
                if event.state == HotKeyState::Pressed {
                    handle_hotkey_event(event.id, Arc::clone(&app_state));
                }
            }
            
            thread::sleep(Duration::from_millis(10));
        }
    });
    
    Ok(())
}

fn handle_hotkey_event(hotkey_id: u32, app_state: Arc<Mutex<AppState>>) {
    if let Ok(mut state) = app_state.lock() {
        match hotkey_id {
            0 => { // Record hotkey (Ctrl+Shift+R)
                if let Some(region) = state.region_selector.get_selected_region() {
                    if let Ok(mut recorder) = state.recorder.lock() {
                        if recorder.is_recording() {
                            recorder.stop_recording();
                            println!("Recording stopped via hotkey");
                        } else {
                            recorder.set_region(region);
                            if let Err(e) = recorder.start_recording() {
                                eprintln!("Failed to start recording via hotkey: {}", e);
                            } else {
                                println!("Recording started via hotkey");
                            }
                        }
                    }
                } else {
                    println!("No region selected for recording");
                }
            }
            1 => { // Select region hotkey (Ctrl+Shift+S)
                state.should_start_selection = true;
                println!("Region selection triggered via hotkey");
            }
            2 => { // Pause/Resume hotkey (Ctrl+Shift+P)
                if let Ok(recorder) = state.recorder.lock() {
                    match recorder.get_state() {
                        crate::recording::RecordingState::Recording => {
                            recorder.pause_recording();
                            println!("Recording paused via hotkey");
                        }
                        crate::recording::RecordingState::Paused => {
                            recorder.resume_recording();
                            println!("Recording resumed via hotkey");
                        }
                        _ => {
                            println!("No active recording to pause/resume");
                        }
                    }
                }
            }
            3 => { // Quick screenshot hotkey (Ctrl+Shift+Q)
                if let Some(region) = state.region_selector.get_selected_region() {
                    if let Err(e) = take_quick_screenshot(region, &state.settings) {
                        eprintln!("Failed to take screenshot: {}", e);
                    } else {
                        println!("Screenshot taken via hotkey");
                    }
                } else {
                    println!("No region selected for screenshot");
                }
            }
            _ => {
                println!("Unknown hotkey pressed: {}", hotkey_id);
            }
        }
    }
}

fn take_quick_screenshot(
    region: crate::region_selector::Rectangle,
    settings: &crate::settings::AppSettings,
) -> Result<(), Box<dyn std::error::Error>> {
    use crate::recording::screen_capture;
    use chrono::Utc;
    
    let screenshot = screen_capture::capture_region(region)?;
    
    let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
    let filename = format!("screenshot_{}.png", timestamp);
    let output_path = settings.download_location.join(filename);
    
    screenshot.save(&output_path)?;
    println!("Screenshot saved to: {:?}", output_path);
    
    Ok(())
}

pub fn get_hotkey_descriptions() -> Vec<(&'static str, &'static str)> {
    vec![
        ("Ctrl+Shift+R", "Start/Stop recording"),
        ("Ctrl+Shift+S", "Select region"),
        ("Ctrl+Shift+P", "Pause/Resume recording"),
        ("Ctrl+Shift+Q", "Quick screenshot"),
    ]
}
